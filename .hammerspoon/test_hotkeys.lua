-- 测试快捷键功能的脚本
print("=== TaskList 快捷键测试 ===")

-- 检查TaskList是否已加载
if not spoon or not spoon.TaskList then
    print("❌ TaskList spoon 未加载")
    print("请先在 init.lua 中加载 TaskList spoon")
    return
end

local taskList = spoon.TaskList

-- 测试快捷键绑定
print("\n1. 测试快捷键绑定")
print("=" .. string.rep("=", 30))

-- 检查快捷键是否存在
if taskList.pauseHotkey then
    print("✅ 暂停快捷键已绑定: Option+Control+P")
else
    print("❌ 暂停快捷键未绑定")
end

if taskList.completeHotkey then
    print("✅ 完成快捷键已绑定: Option+Control+D")
else
    print("❌ 完成快捷键未绑定")
end

-- 测试快捷键功能
print("\n2. 测试快捷键功能")
print("=" .. string.rep("=", 30))

-- 模拟按下暂停快捷键
print("模拟按下 Option+Control+P...")
if taskList.pauseHotkey and taskList.pauseHotkey.fn then
    taskList.pauseHotkey.fn()
    print("✅ 暂停快捷键功能已执行")
else
    print("❌ 暂停快捷键功能无法执行")
end

-- 等待一下
hs.timer.usleep(1000000)  -- 1秒

-- 模拟按下完成快捷键
print("\n模拟按下 Option+Control+D...")
if taskList.completeHotkey and taskList.completeHotkey.fn then
    taskList.completeHotkey.fn()
    print("✅ 完成快捷键功能已执行")
else
    print("❌ 完成快捷键功能无法执行")
end

print("\n3. 检查控制台日志")
print("=" .. string.rep("=", 30))
print("请检查 Hammerspoon 控制台是否有以下日志:")
print("- TaskList: Pause hotkey triggered")
print("- TaskList: Complete task hotkey triggered")

print("\n=== 测试完成 ===")
print("如果看到快捷键绑定成功和功能执行，说明快捷键正常工作。")
print("请手动按下 Option+Control+P 和 Option+Control+D 测试实际效果。")
