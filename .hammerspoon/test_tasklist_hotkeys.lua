-- 在Hammerspoon控制台中运行此脚本来测试快捷键
print("=== TaskList 快捷键实际测试 ===")

-- 创建测试快捷键来验证功能
local testPauseHotkey = hs.hotkey.bind({"alt", "ctrl"}, "t", function()
    print("测试快捷键触发成功！")
    hs.alert.show("测试快捷键工作正常", 2)
end)

print("✅ 测试快捷键已创建: Option+Control+T")
print("请按 Option+Control+T 测试快捷键系统是否正常工作")

-- 5秒后清理测试快捷键
hs.timer.doAfter(10, function()
    if testPauseHotkey then
        testPauseHotkey:delete()
        print("🧹 测试快捷键已清理")
    end
end)

-- 检查TaskList是否已加载
if spoon and spoon.TaskList then
    print("✅ TaskList spoon 已加载")
    
    -- 检查快捷键是否存在
    if spoon.TaskList.pauseHotkey then
        print("✅ TaskList 暂停快捷键已绑定")
    else
        print("❌ TaskList 暂停快捷键未绑定")
    end
    
    if spoon.TaskList.completeHotkey then
        print("✅ TaskList 完成快捷键已绑定")
    else
        print("❌ TaskList 完成快捷键未绑定")
    end
    
    print("\n现在请测试 TaskList 快捷键:")
    print("- Option+Control+P: 暂停/恢复倒计时")
    print("- Option+Control+D: 完成当前任务")
    print("\n观察控制台是否有 'TaskList: xxx hotkey triggered' 日志输出")
    
else
    print("❌ TaskList spoon 未加载")
    print("请确保在 init.lua 中正确加载了 TaskList spoon")
end
